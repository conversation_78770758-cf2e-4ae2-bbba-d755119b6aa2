import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/responsive_helper.dart';

class RegistrationCompletePage extends StatelessWidget {
  final String title;
  final String? description;
  final VoidCallback? onConfirm;
  final VoidCallback? onClose;

  const RegistrationCompletePage({
    super.key,
    this.title = '등록이 완료되었습니다!',
    this.description,
    this.onConfirm,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Safe<PERSON><PERSON>(
          child: Center(
            child: Container(
              width: ResponsiveHelper.getCardMaxWidth(context),
              margin: ResponsiveHelper.getScreenPadding(context),
              padding: ResponsiveHelper.getCardPadding(context),
              decoration: BoxDecoration(
                gradient: AppColors.cardGradient,
                borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.elevation3,
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // bara_icon.png로 완전 교체 (스플래시 스크린과 동일한 방식)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(
                      isTablet ? 24.0 : 20.0,
                    ),
                    child: Image.asset(
                      'assets/images/bara_icon.png',
                      width: isTablet ? 120.0 : 100.0,
                      height: isTablet ? 120.0 : 100.0,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // 이미지 로드 실패 시 기본 원형 아이콘 표시
                        return Container(
                          width: isTablet ? 120.0 : 100.0,
                          height: isTablet ? 120.0 : 100.0,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppColors.success.withValues(alpha: 0.1),
                                AppColors.success.withValues(alpha: 0.05),
                              ],
                            ),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppColors.success.withValues(alpha: 0.3),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            Icons.check_circle_rounded,
                            color: AppColors.success,
                            size: isTablet ? 80.0 : 64.0,
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: isTablet ? 32.0 : 24.0),

                  // 제목
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: isTablet ? 28.0 : 24.0,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                      letterSpacing: -0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  // 설명
                  if (description != null) ...[
                    SizedBox(height: isTablet ? 20.0 : 16.0),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isTablet ? 24.0 : 20.0,
                        vertical: isTablet ? 20.0 : 16.0,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.surface.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.neutral30.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        description!,
                        style: TextStyle(
                          fontSize: isTablet ? 16.0 : 14.0,
                          color: AppColors.onSurfaceVariant,
                          height: 1.6,
                          letterSpacing: 0.1,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],

                  SizedBox(height: isTablet ? 48.0 : 40.0),

                  // 확인 버튼 (프로젝트 디자인에 맞게)
                  Container(
                    width: double.infinity,
                    height: isTablet ? 56.0 : 48.0,
                    decoration: BoxDecoration(
                      gradient: AppColors.primaryGradient,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primarySeed.withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: onConfirm ?? () => Navigator.of(context).pop(),
                        borderRadius: BorderRadius.circular(20),
                        child: Center(
                          child: Text(
                            '확인',
                            style: TextStyle(
                              fontSize: isTablet ? 18.0 : 16.0,
                              fontWeight: FontWeight.w600,
                              color: AppColors.onPrimary,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 닫기 버튼 (텍스트 버튼)
                  if (onClose != null) ...[
                    SizedBox(height: isTablet ? 16.0 : 12.0),
                    TextButton(
                      onPressed: onClose,
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          horizontal: isTablet ? 24.0 : 20.0,
                          vertical: isTablet ? 12.0 : 10.0,
                        ),
                      ),
                      child: Text(
                        '닫기',
                        style: TextStyle(
                          fontSize: isTablet ? 16.0 : 14.0,
                          color: AppColors.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 
